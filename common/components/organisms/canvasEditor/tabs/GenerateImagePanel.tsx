'use client'

import React, { 
  useEffect, useRef, useState,
} from 'react';
import Konva from 'konva';
import autosize from 'autosize';
import { ImageStyleGroups } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';

interface GenerateImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

const scaleImageToFitCanvas = (
  img: Konva.Image,
  canvas: Konva.Stage,
  containerRef?: React.RefObject<HTMLDivElement>,
  zoomLevel?: number,
) => {

  let layer = canvas.findOne('Layer') as Konva.Layer;
  if (!layer) {
    layer = new Konva.Layer();
    canvas.add(layer);
  } else {
  }

  const canvasWidth = canvas.width();
  const canvasHeight = canvas.height();
  const imageElement = img.image() as HTMLImageElement;
  const imgWidth = imageElement?.width || 1;
  const imgHeight = imageElement?.height || 1;

  const scaleX = canvasWidth / imgWidth;
  const scaleY = canvasHeight / imgHeight;
  let scale = Math.min(scaleX, scaleY, 1);

  if (containerRef?.current && zoomLevel !== undefined) {
    scale = Math.min(scale * (zoomLevel || 1), 1.3);
  }

  img.x((canvasWidth - imgWidth * scale) / 2);
  img.y((canvasHeight - imgHeight * scale) / 2);
  img.scaleX(scale);
  img.scaleY(scale);
  img.draggable(true);

  layer.add(img);

  let transformer = layer.findOne('Transformer') as Konva.Transformer;
  if (!transformer) {
    transformer = new Konva.Transformer();
    layer.add(transformer);
  } else {
  }

  transformer.nodes([img]);

  canvas.batchDraw();
};

export const GenerateImagePanel = ({
  canvas,
  agentId,
  planId,
  containerRef,
  zoomLevel,
}: GenerateImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof ImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof ImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const requestBody: any = {
        description: imagePrompt,
        planId: planId || 'new-post',
        seed: seed,
        guidanceScale: guidanceScale,
      };

      if (selectedStyle && selectedStyle.option !== 'none') {
        requestBody.style = [selectedStyle.option];
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        toast.error('Failed to generate image');
        setError(`${errorData.error.includes("NSFW") ? `${errorData.error} ` : ""}` || 'Failed to generate image');
        setIsGenerating(false);
        return
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const imageUrl = getPath(imageData.filepath);

        const imageObj = new window.Image();
        imageObj.crossOrigin = 'anonymous';
        imageObj.onload = () => {
          const konvaImage = new Konva.Image({
            image: imageObj,
          });
          scaleImageToFitCanvas(konvaImage, canvas, containerRef, zoomLevel);
        };
        imageObj.onerror = (error) => {
          console.error('Failed to load image:', error);
          toast.error('Failed to load generated image');
        };
        imageObj.src = imageUrl;

        if (activeProject?.project_id && agentId) {
          const fileName = `Generated Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          await projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            imageUrl,
            fileName,
            planId,
            imagePrompt,
          );
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Image generated and added to canvas!');
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Image Style</h3>
            <p className="text-gray-400 text-sm">Create and add images using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(ImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-xl border transition-all duration-200 text-left group aspect-square border-neutral-600 hover:border-neutral-500 bg-neutral-800"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              {style.option === "none" ? (
                                <div className="flex items-center justify-center h-full rounded-xl bg-gradient-to-br from-neutral-800 to-neutral-700 text-white text-xs font-medium">
                                  Create Your Own Style
                                </div>
                              ) : (
                                <Image
                                  src={`/images/style-samples/${style.option}.png`}
                                  alt={style.label}
                                  width={200}
                                  height={200}
                                  quality={30}
                                  className='rounded-xl h-full w-full object-cover'
                                />
                              )}
                            </div>
                            <div className="text-[10px] absolute bottom-2 right-2 font-medium text-neutral-900 bg-white/90 rounded-lg py-1 px-2 backdrop-blur-sm truncate shadow-sm max-w-[calc(100%-16px)]">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Generate Image</h3>
                <p className="text-gray-400 text-sm">
                  Style: {selectedStyle?.label || 'None'}
                </p>
              </div>
            </div>
          </div>
          <div className='flex flex-col space-y-4'>
            <label htmlFor="image-prompt" className="text-white font-medium text-sm">
              Describe your image
              <TextArea
                ref={imagePromptRef}
                name="image-prompt"
                id="image-prompt"
                width='w-full'
                maxHeight='80px'
                disabled={isGenerating}
                placeholder='Describe the image you want to generate...'
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
              />
            </label>
            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
                
              </div>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Guidance Scale: {guidanceScale}
              </label>
              <input
                type="range"
                min="0"
                max="20"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              onClick={handleGenerate}
              disabled={isGenerating || !imagePrompt.trim()}
              variant='gradient'
              size='md'
              width='w-full'
            >
              {isGenerating ? 'Generating...' : 'Generate Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
          <div className="text-xs mt-4 text-gray-500 bg-neutral-800 p-3 rounded-xl">
            <p className="mb-1">💡 <strong>Tips:</strong></p>
            <p>• Higher guidance scale = more adherence to your prompt</p>
            <p>• Different seeds produce different variations</p>
          </div>
        </>
      )}
    </div>
  );
};
